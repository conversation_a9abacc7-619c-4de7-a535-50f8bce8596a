package cn.jiawen.payment.controller;

import cn.jiawen.payment.service.WxPayService;
import cn.jiawen.payment.util.HttpUtils;
import cn.jiawen.payment.util.WechatPay2ValidatorForRequest;
import com.google.gson.Gson;
import com.wechat.pay.contrib.apache.httpclient.auth.Verifier;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vo.AjaxResult;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@CrossOrigin
@RestController
@RequestMapping("/api/wx-pay")
@Api(tags = "网站微信支付APIv3")
public class WxPayController {

    private static final Logger log = LoggerFactory.getLogger(WxPayController.class);

    @Resource
    private WxPayService wxPayService;

    @Resource
    private Verifier verifier;


    @ApiOperation("上传图片到微信服务器")
    @PostMapping("/media-upload")
    public AjaxResult mediaUpload(@RequestParam("file") MultipartFile file) throws Exception {

        String mediaId = wxPayService.mediaUpload(file);

        return AjaxResult.success(mediaId);
    }

    @ApiOperation("商家入驻申请")
    @PostMapping("/applyment")
    public AjaxResult applyment(@RequestBody Map<String, String> body) throws Exception{

        log.info("商家入驻申请{}", body);

        // 返回微信支付分配的申请单号
        String applymentNo = wxPayService.applyment(body);

        return AjaxResult.success(applymentNo);
    }


    /*
    * 统一下单API
    * @param: productId 商品id
    * @return: 支付二维码链接和订单号
    * */
    @ApiOperation("调用统一下单API，生成支付二维码")
    @PostMapping("/native/{productId}")
    public AjaxResult naivePay(@PathVariable Long productId) throws Exception {

        log.info("发起支付请求，商品id:{}",productId);

        // 返回支付二维码链接和订单号
        Map<String, Object> map = wxPayService.nativePay(productId);

        return AjaxResult.success(map);
    }

    /*
     * 支付回调通知
     * */
    @ApiOperation("支付回调通知")
    @PostMapping("/native/notify")
    public String nativeNotify(HttpServletRequest request, HttpServletResponse response) {

        Gson gson = new Gson();
        HashMap<String, String> map = new HashMap<>();  // 应答对象

        try {

            String body = HttpUtils.readData(request);
            HashMap<String, Object> bodyMap = gson.fromJson(body, HashMap.class);
            String requestId = (String) bodyMap.get("id");
            log.info("支付通知的id ===> {}", bodyMap.get("id"));
            log.info("支付通知的完整数据 ===> {}", bodyMap);

            // 签名验证
            WechatPay2ValidatorForRequest wechatPay2ValidatorForRequest = new WechatPay2ValidatorForRequest(verifier, requestId, body);
            if (!wechatPay2ValidatorForRequest.validate(request)) {

                log.error("通知验签失败");

                // 失败应答
                response.setStatus(500);
                map.put("code", "failed");
                map.put("message", "通知验签失败");
                return gson.toJson(map);
            }
            log.info("通知验签成功");

            // 处理订单
            // 响应数据解密
            wxPayService.processOrder(bodyMap);

            // 成功应答
            response.setStatus(200);
            map.put("code", "success");
            map.put("message", "成功");
            return gson.toJson(map);

        } catch (Exception e) {
            e.printStackTrace();
            // 失败应答
            response.setStatus(500);
            map.put("code", "failed");
            map.put("message", "失败");
            return gson.toJson(map);
        }
    }

    /*
     * 用户取消订单
     * @param: orderNo 订单编号
     * @return
     * */
    @ApiOperation("用户取消订单")
    @PostMapping("/cancel/{orderNo}")
    public AjaxResult cancel(@PathVariable String orderNo) throws Exception {

        log.info("取消订单");

        wxPayService.cancelOrder(orderNo);
        return AjaxResult.success("订单已取消");

    }

    /*
     * 查询订单
     * @param: orderNo 订单编号
     * @return
     * */
    @ApiOperation("查询订单：测试用")  // 该接口由定时任务自动调用
    @GetMapping("/query/{orderNo}")
    public AjaxResult queryOrder(@PathVariable String orderNo) throws Exception {

        log.info("查询订单");

        String res = wxPayService.queryOrder(orderNo);
        return AjaxResult.success("查询成功", res);

    }

    /*
     * 查询退款
     * @param: orderNo 订单编号
     * @param: reason 退款原因
     * @return
     * */
    @ApiOperation("查询退款：测试用") // 该接口由定时任务自动调用
    @PostMapping("/query-refund/{refundNo}")
    public AjaxResult queryRefund(@PathVariable String refundNo) throws Exception {

        log.info("查询退款");

        String res = wxPayService.queryRefund(refundNo);

        return AjaxResult.success("查询成功", res);
    }

    /**
     * 退款回调通知
     * 退款状态改变后，微信会把相关退款结果发送给商户。
     */
    @ApiOperation("退款结果通知")
    @PostMapping("/refunds/notify")
    public String refundsNotify(HttpServletRequest request, HttpServletResponse response){

        log.info("退款通知执行");
        Gson gson = new Gson();
        Map<String, String> map = new HashMap<>(); // 应答对象

        try {
            //处理通知参数
            String body = HttpUtils.readData(request);
            Map<String, Object> bodyMap = gson.fromJson(body, HashMap.class);
            String requestId = (String)bodyMap.get("id");
            log.info("退款通知的id ===> {}", requestId);

            //签名的验证
            WechatPay2ValidatorForRequest wechatPay2ValidatorForRequest
                    = new WechatPay2ValidatorForRequest(verifier, requestId, body);
            if(!wechatPay2ValidatorForRequest.validate(request)){

                log.error("通知验签失败");
                //失败应答
                response.setStatus(500);
                map.put("code", "ERROR");
                map.put("message", "通知验签失败");
                return gson.toJson(map);
            }
            log.info("通知验签成功");

            //处理退款单
            wxPayService.processRefund(bodyMap);

            //成功应答
            response.setStatus(200);
            map.put("code", "SUCCESS");
            map.put("message", "成功");
            return gson.toJson(map);

        } catch (Exception e) {
            e.printStackTrace();
            //失败应答
            response.setStatus(500);
            map.put("code", "ERROR");
            map.put("message", "失败");
            return gson.toJson(map);
        }
    }

    /*
     * 获取账单url（交易/资金）
     * @param: billDate 账单日期
     * @param: type 账单类型
     * @return
     * */
    @ApiOperation("获取账单url：测试用")
    @GetMapping("/querybill/{billDate}/{type}")
    public AjaxResult queryTradeBill(
            @PathVariable String billDate,
            @PathVariable String type) throws Exception {

        log.info("获取账单url");

        String downloadUrl = wxPayService.queryBill(billDate, type);
        return AjaxResult.success("获取账单url成功", downloadUrl);
    }

    /*
     * 下载账单url（交易/资金）
     * @param: billDate 账单日期
     * @param: type 账单类型
     * @return
     * */
    @ApiOperation("下载账单")
    @GetMapping("/downloadbill/{billDate}/{type}")
    public AjaxResult downloadBill(
            @PathVariable String billDate,
            @PathVariable String type) throws Exception {

        log.info("下载账单");
        String result = wxPayService.downloadBill(billDate, type);

        return AjaxResult.success("下载账单成功", result);
    }

}

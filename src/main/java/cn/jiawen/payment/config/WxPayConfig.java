package cn.jiawen.payment.config;


import cn.jiawen.payment.util.WxPayEncryptUtil;
import com.wechat.pay.contrib.apache.httpclient.WechatPayHttpClientBuilder;
import com.wechat.pay.contrib.apache.httpclient.auth.PrivateKeySigner;
import com.wechat.pay.contrib.apache.httpclient.auth.Verifier;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Credentials;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Validator;
import com.wechat.pay.contrib.apache.httpclient.cert.CertificatesManager;
import com.wechat.pay.contrib.apache.httpclient.exception.HttpCodeException;
import com.wechat.pay.contrib.apache.httpclient.exception.NotFoundException;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.PrivateKey;


@Slf4j
@Configuration
@PropertySource("classpath:wxpay.properties") //读取配置文件
@ConfigurationProperties(prefix = "wxpay") //读取wxpay节点
@Data //使用set方法将wxpay节点中的值填充到当前类的属性中
public class WxPayConfig {

    // 服务商商户号
    private String spMchid;

    // 服务商商户API证书序列号
    private String mchSerialNo;

    // 服务商商户私钥文件
    private String privateKeyPath;

    // 微信支付公钥文件路径
    private String publicKeyPath;

    // 微信支付公钥ID
    private String publicKeyId;

    // APIv3密钥
    private String apiV3Key;

    // 服务商APPID
    public String spAppid;

    // 微信服务器地址
    private String domain;

    // 接收结果通知地址
    private String notifyDomain;


    /**
     * 加载商户的私钥文件
     *
     * @param fileName 私钥文件路径
     * @return 私钥
     */
    private PrivateKey getPrivateKey(String fileName) {
        try {
            return PemUtil.loadPrivateKey(new FileInputStream(fileName));
        } catch (FileNotFoundException e) {
            throw new RuntimeException("私钥文件不存在", e);
        }
    }

    /**
     * 加载微信支付公钥文件
     *
     * @param fileName 公钥文件路径
     * @return 公钥
     */
    private java.security.PublicKey getPublicKey(String fileName) {
        try {
            return PemUtil.loadPublicKey(new FileInputStream(fileName));
        } catch (FileNotFoundException e) {
            throw new RuntimeException("公钥文件不存在", e);
        }
    }

    /**
     * 获取微信支付公钥ID
     * @return 公钥ID
     */
    @Bean(name = "wxPayPublicKeyId")
    protected String getWxPayPublicKeyId() {
        return publicKeyId;
    }


    /**
     * 获取签名验证器（使用微信支付公钥模式）
     *
     * @return
     */
    @Bean
    public Verifier getVerifier() throws NotFoundException, GeneralSecurityException, IOException, HttpCodeException {
        // 加载微信支付公钥
        java.security.PublicKey publicKey = getPublicKey(publicKeyPath);

        // 创建一个简单的验证器实现，直接使用公钥验证
        return new Verifier() {
            @Override
            public boolean verify(String serialNumber, byte[] message, String signature) {
                // 如果序列号匹配公钥ID，则使用公钥验证
                if (publicKeyId.equals(serialNumber)) {
                    try {
                        java.security.Signature sig = java.security.Signature.getInstance("SHA256withRSA");
                        sig.initVerify(publicKey);
                        sig.update(message);
                        return sig.verify(java.util.Base64.getDecoder().decode(signature));
                    } catch (Exception e) {
                        log.error("验证签名失败", e);
                        return false;
                    }
                }
                return false;
            }

            @Override
            public String getSerialNumber() {
                return publicKeyId;
            }

            @Override
            public java.security.PublicKey getValidPublicKey() {
                return publicKey;
            }
        };
    }

    /**
     * 获取http请求对象（需要签名验证）- 使用公钥模式
     * @return
     */
    @Bean
    public CloseableHttpClient getWxPayClient(Verifier verifier) {

        PrivateKey privateKey = getPrivateKey(privateKeyPath);

        WechatPayHttpClientBuilder builder = WechatPayHttpClientBuilder.create()
                .withMerchant(spMchid, mchSerialNo, privateKey)
                .withValidator(new WechatPay2Validator(verifier));

        // 通过WechatPayHttpClientBuilder构造的HttpClient，会自动的处理签名和验签
        CloseableHttpClient httpClient = builder.build();

        log.info("微信支付客户端初始化完成，使用公钥模式，公钥ID: {}", publicKeyId);
        return httpClient;
    }


    /**
     * 使用微信支付公钥加密敏感信息
     * @param sensitiveInfo 敏感信息明文
     * @return 加密后的Base64字符串
     */
    public String encryptSensitiveInfo(String sensitiveInfo) {
        try {
            java.security.PublicKey publicKey = getPublicKey(publicKeyPath);
            return WxPayEncryptUtil.encryptSensitiveInfo(sensitiveInfo, publicKey);
        } catch (Exception e) {
            log.error("加密敏感信息失败", e);
            throw new RuntimeException("加密敏感信息失败", e);
        }
    }

    /**
     * 获取HttpClient，无需进行应答签名验证，跳过验签的流程
     */
    @Bean(name = "wxPayNoSignClient")
    public CloseableHttpClient getWxPayNoSignClient(){

        //获取商户私钥
        PrivateKey privateKey = getPrivateKey(privateKeyPath);

        //用于构造HttpClient
        WechatPayHttpClientBuilder builder = WechatPayHttpClientBuilder.create()
                //设置商户信息
                .withMerchant(spMchid, mchSerialNo, privateKey)
                //无需进行签名验证、通过withValidator实现
                .withValidator(new WechatPay2Validator(new Verifier() {
                    @Override
                    public boolean verify(String serialNumber, byte[] message, String signature) {
                        return true; // 跳过验签
                    }

                    @Override
                    public String getSerialNumber() {
                        return publicKeyId;
                    }

                    @Override
                    public java.security.PublicKey getValidPublicKey() {
                        return getPublicKey(publicKeyPath);
                    }
                }));

        // 通过WechatPayHttpClientBuilder构造的HttpClient，会自动的处理签名和验签，并进行证书自动更新
        CloseableHttpClient httpClient = builder.build();

        log.info("== getWxPayNoSignClient END ==");

        return httpClient;
    }

}
